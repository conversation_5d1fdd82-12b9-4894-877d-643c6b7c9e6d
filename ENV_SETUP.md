# Environment Configuration

This project uses environment variables from the `.env` file instead of hardcoded values in `eas.json`. This makes it easy to manage configuration and keep sensitive data secure.

## 📁 Configuration File

### `.env`
- Contains all environment variables for the app
- Set to production values by default
- Can be easily modified for different environments

## 🚀 Usage

### Building the App
```bash
# Preview build
npm run build:preview

# Production build
npm run build:prod

# Android APK
npm run build:apk

# Android App Bundle (AAB)
npm run build:aab
```

### Local Development
For local development, simply uncomment the development URLs in `.env`:

```bash
# Comment out production URLs
# EXPO_PUBLIC_WEBSITE_DOMAIN=https://api.shashtrarth.com
# EXPO_PUBLIC_BASE_URL=https://api.shashtrarth.com/

# Uncomment development URLs
EXPO_PUBLIC_WEBSITE_DOMAIN=http://127.0.0.1:8000
EXPO_PUBLIC_BASE_URL=http://127.0.0.1:8000/
```

Then run:
```bash
npm start
```

## 📝 Adding New Environment Variables

1. **Add to `.env` file:**
   ```
   EXPO_PUBLIC_YOUR_NEW_VARIABLE=your_value
   ```

2. **Add to `eas.json`:**
   ```json
   "env": {
     "EXPO_PUBLIC_YOUR_NEW_VARIABLE": "$EXPO_PUBLIC_YOUR_NEW_VARIABLE"
   }
   ```

3. **Use in your app:**
   ```javascript
   const myVariable = process.env.EXPO_PUBLIC_YOUR_NEW_VARIABLE;
   ```

## 🔒 Security Notes

- Always use `EXPO_PUBLIC_` prefix for variables that need to be available in the client
- Keep the `.env` file secure and don't commit sensitive production values to public repositories
- Consider using EAS Secrets for highly sensitive data

## 🌍 Current Environment Variables

| Variable | Description | Current Value |
|----------|-------------|---------------|
| `EXPO_PUBLIC_WEBSITE_DOMAIN` | API domain | `https://api.shashtrarth.com` |
| `EXPO_PUBLIC_BASE_URL` | API base URL | `https://api.shashtrarth.com/` |
| `EXPO_PUBLIC_BLOGS` | Blogs endpoint | `api/blogs/public-blogs/` |
| `EXPO_PUBLIC_FIREBASE_*` | Firebase configuration | Production values |
| `EXPO_PUBLIC_*_CLIENT_ID` | OAuth client IDs | Production values |

## 🔄 Benefits

- ✅ **No hardcoded values** in configuration files
- ✅ **Easy to modify** - just edit the `.env` file
- ✅ **Better security** - sensitive values in one place
- ✅ **Simple setup** - one file to manage
- ✅ **Flexible** - easy to switch between environments by commenting/uncommenting lines
