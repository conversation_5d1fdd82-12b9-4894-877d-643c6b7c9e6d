import React, { useContext, useState, useEffect } from 'react';
import { TouchableOpacity, Alert } from 'react-native';
import * as Icons from '@expo/vector-icons';
import { useDispatch, useSelector } from 'react-redux';
import { ThemeContext } from '../context/ThemeContext';
import { FolderSelectionModal } from './FolderSelectionModal';
import { saveBlog, unsaveBlog, checkSavedStatus } from '../redux/blogSaveSlice';

export const SaveButton = ({ post, style, navigation }) => {
  const dispatch = useDispatch();
  const { isDarkMode } = useContext(ThemeContext);
  const { isAuthenticated } = useSelector((state) => state.auth);
  const { savedStatus, needsAuth } = useSelector((state) => state.blogSave);

  const [showFolderModal, setShowFolderModal] = useState(false);

  const postSlug = post?.slug;
  const savedInfo = savedStatus[postSlug] || { isSaved: false, savedBlogId: null };
  const isSaved = savedInfo.isSaved;

  useEffect(() => {
    if (postSlug && isAuthenticated) {
      dispatch(checkSavedStatus(postSlug));
    }
  }, [postSlug, isAuthenticated, dispatch]);

  useEffect(() => {
    if (needsAuth && navigation) {
      Alert.alert(
        'Login Required',
        'Please log in to save articles to your collection.',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Login',
            onPress: () => navigation.navigate('Auth')
          }
        ]
      );
    }
  }, [needsAuth, navigation]);

  const handleSave = () => {
    if (!post?.slug) return;

    if (!isAuthenticated) {
      Alert.alert(
        'Login Required',
        'Please log in to save articles to your collection.',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Login',
            onPress: () => navigation?.navigate('Auth')
          }
        ]
      );
      return;
    }

    if (isSaved) {
      // Unsave the article
      Alert.alert(
        'Remove Article',
        'Remove this article from your saved collection?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Remove',
            style: 'destructive',
            onPress: () => {
              if (savedInfo.savedBlogId) {
                dispatch(unsaveBlog(savedInfo.savedBlogId));
              }
            }
          }
        ]
      );
    } else {
      // Show folder selection modal
      setShowFolderModal(true);
    }
  };

  const handleFolderSelect = async (folderId) => {
    try {
      await dispatch(saveBlog({
        slug: post.slug,
        folderId,
        notes: ''
      })).unwrap();
      setShowFolderModal(false);
    } catch (error) {
      // Error is handled in the slice with toast
      setShowFolderModal(false);
    }
  };

  return (
    <>
      <TouchableOpacity onPress={handleSave} style={style}>
        <Icons.Ionicons
          name={isSaved ? "bookmark" : "bookmark-outline"}
          size={24}
          color={isDarkMode ? '#ffffff' : '#ebebe0'}
        />
      </TouchableOpacity>

      <FolderSelectionModal
        visible={showFolderModal}
        onClose={() => setShowFolderModal(false)}
        onSelectFolder={handleFolderSelect}
      />
    </>
  );
};
