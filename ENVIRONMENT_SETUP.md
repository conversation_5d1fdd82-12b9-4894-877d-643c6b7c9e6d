# Environment Configuration Guide

This project now uses environment variables from `.env` files instead of hardcoded values in `eas.json`. This makes it easier to manage different environments and keep sensitive data secure.

## 📁 Environment Files

### `.env` (Current Active Environment)
- This is the currently active environment file
- Gets copied from either `.env.development` or `.env.production`
- **Do not edit this file directly** - use the environment-specific files instead

### `.env.development` 
- Contains development/local environment variables
- Uses local server URLs (http://127.0.0.1:8000)
- Used for local development and testing

### `.env.production`
- Contains production environment variables  
- Uses production server URLs (https://api.shashtrarth.com)
- Used for production builds and releases

## 🚀 Usage

### Switching Environments

#### For Development
```bash
npm run env:dev
```
This copies `.env.development` to `.env`

#### For Production
```bash
npm run env:prod
```
This copies `.env.production` to `.env`

### Building with Environments

#### Development Build
```bash
npm run build:dev
```
Automatically switches to development environment and builds

#### Production Builds
```bash
# Standard production build
npm run build:prod

# Android APK
npm run build:apk

# Android App Bundle (AAB)
npm run build:aab

# Preview build
npm run build:preview
```

All production builds automatically switch to production environment first.

## 🔧 Manual Environment Management

If you prefer manual control:

1. **Copy environment file:**
   ```bash
   # For development
   cp .env.development .env
   
   # For production  
   cp .env.production .env
   ```

2. **Build with EAS:**
   ```bash
   eas build --profile production
   ```

## 📝 Adding New Environment Variables

1. **Add to both environment files:**
   - Add the variable to `.env.development`
   - Add the variable to `.env.production`

2. **Update eas.json:**
   ```json
   "env": {
     "YOUR_NEW_VARIABLE": "$YOUR_NEW_VARIABLE"
   }
   ```

3. **Use in your app:**
   ```javascript
   const myVariable = process.env.EXPO_PUBLIC_YOUR_NEW_VARIABLE;
   ```

## 🔒 Security Notes

- **Never commit sensitive production values** to version control
- The `.env.production` file contains example values - replace with real production values
- Consider using EAS Secrets for highly sensitive data
- Always use `EXPO_PUBLIC_` prefix for variables that need to be available in the client

## 🌍 Environment Variables Reference

| Variable | Description | Development | Production |
|----------|-------------|-------------|------------|
| `EXPO_PUBLIC_WEBSITE_DOMAIN` | API domain | `http://127.0.0.1:8000` | `https://api.shashtrarth.com` |
| `EXPO_PUBLIC_BASE_URL` | API base URL | `http://127.0.0.1:8000/` | `https://api.shashtrarth.com/` |
| `EXPO_PUBLIC_BLOGS` | Blogs endpoint | `api/blogs/public-blogs/` | `api/blogs/public-blogs/` |
| `EXPO_PUBLIC_FIREBASE_*` | Firebase config | Same for both environments | Same for both environments |
| `EXPO_PUBLIC_*_CLIENT_ID` | OAuth client IDs | Same for both environments | Same for both environments |

## 🔄 Migration from Hardcoded Values

The `eas.json` file has been updated to use environment variable references (`$VARIABLE_NAME`) instead of hardcoded values. This provides:

- ✅ **Easy environment switching**
- ✅ **Better security** (no hardcoded secrets)
- ✅ **Simplified maintenance**
- ✅ **Consistent configuration**

## 🆘 Troubleshooting

### Build fails with "undefined" values
- Ensure you've run `npm run env:dev` or `npm run env:prod` before building
- Check that all required variables are defined in your environment file

### Variables not updating
- Make sure you're copying the correct environment file to `.env`
- Restart your development server after changing environment variables

### EAS Build issues
- Verify that all variables in `eas.json` have corresponding entries in your environment files
- Check EAS build logs for specific error messages about missing variables
