import React, { useEffect, useState, useContext } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  SafeAreaView,
  useWindowDimensions,
} from "react-native";
import { useDispatch, useSelector } from "react-redux";
import RenderHtml from "react-native-render-html";
import { LoadingSpinner } from "../components/LoadingSpinner";
import { getPost } from "../redux/postsSlice";
import { ShareButton } from "../components/ShareButton";
import { SaveButton } from "../components/SaveButton";
import { ThemeContext } from "../context/ThemeContext";
import { LinearGradient } from "expo-linear-gradient";
import Animated, { FadeIn } from "react-native-reanimated";

export default function BlogPostScreen({ navigation, route }) {
  const { slug } = route.params;
  const dispatch = useDispatch();
  const { currentPost, isLoading, error } = useSelector((state) => state.posts);
  const [imageError, setImageError] = useState(false);
  const { width } = useWindowDimensions();
  const { isDarkMode } = useContext(ThemeContext);

  useEffect(() => {
    dispatch(getPost(slug));
  }, [dispatch, slug]);

  const getFullImageUrl = (imageUrl) => {
    if (!imageUrl) return null;
    return imageUrl.startsWith("http")
      ? imageUrl
      : `${process.env.EXPO_PUBLIC_BASE_URL}${imageUrl}`.replace(
        /([^:]\/)\/+/g,
        "$1"
      );
  };

  const renderersProps = {
    img: {
      enableExperimentalPercentWidth: true,
    },
  };

  const getTagsStyles = () => ({
    body: {
      color: isDarkMode ? "#ffffff" : "#000000",
      backgroundColor: "transparent",
    },
    p: {
      color: isDarkMode ? "#e0e0e0" : "#333333",
      fontSize: 16,
      lineHeight: 24,
      marginBottom: 15,
    },
    h1: {
      color: isDarkMode ? "#ffffff" : "#000000",
      fontSize: 24,
      fontWeight: "bold",
      marginVertical: 10,
    },
    h2: {
      color: isDarkMode ? "#ffffff" : "#000000",
      fontSize: 22,
      fontWeight: "bold",
      marginVertical: 8,
    },
    h3: {
      color: isDarkMode ? "#ffffff" : "#000000",
      fontSize: 20,
      fontWeight: "bold",
      marginVertical: 6,
    },
    a: {
      color: isDarkMode ? "#66b2ff" : "#007AFF",
      textDecorationLine: "underline",
    },
    li: {
      color: isDarkMode ? "#e0e0e0" : "#333333",
      marginBottom: 8,
    },
    ul: {
      marginBottom: 15,
    },
    ol: {
      marginBottom: 15,
    },
    pre: {
      backgroundColor: isDarkMode ? "#1e1e1e" : "#f5f5f5",
      padding: 15,
      borderRadius: 8,
      marginVertical: 10,
    },
    code: {
      backgroundColor: isDarkMode ? "#1e1e1e" : "#f5f5f5",
      color: isDarkMode ? "#e0e0e0" : "#333333",
      fontFamily: "monospace",
      padding: 4,
      borderRadius: 4,
    },
    blockquote: {
      borderLeftWidth: 4,
      borderLeftColor: isDarkMode ? "#404040" : "#e0e0e0",
      paddingLeft: 16,
      marginLeft: 0,
      marginVertical: 10,
      fontStyle: "italic",
      color: isDarkMode ? "#cccccc" : "#666666",
    },
    img: {
      marginVertical: 10,
      borderRadius: 8,
    },
  });

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <SafeAreaView
        style={[styles.container, isDarkMode && styles.containerDark]}>
        <Text style={[styles.errorText, isDarkMode && styles.errorTextDark]}>
          {typeof error === "string" ? error : "Failed to load post"}
        </Text>
      </SafeAreaView>
    );
  }

  const tagsStyles = getTagsStyles();

  return (
    <SafeAreaView
      style={[styles.container, isDarkMode && styles.containerDark]}>
      <ScrollView
        style={[styles.content, isDarkMode && styles.contentDark]}
        showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <View style={styles.imageContainer}>
            <Image
              source={
                currentPost?.image
                  ? { uri: getFullImageUrl(currentPost.image) }
                  : require("../assets/placeholder.png")
              }
              style={styles.image}
              resizeMode="cover"
              onError={() => setImageError(true)}
            />
            <Animated.View
              style={styles.imageOverlay}
              entering={FadeIn.duration(300)}>
              <LinearGradient
                colors={["transparent", "rgba(0,0,0,0.7)"]}
                style={styles.gradient}>
                <View style={styles.actions}>
                  <ShareButton
                    post={currentPost}
                    isDarkMode={true}
                    style={styles.actionButton}
                  />
                  <SaveButton
                    post={currentPost}
                    isDarkMode={true}
                    style={styles.actionButton}
                  />
                </View>
              </LinearGradient>
            </Animated.View>
          </View>
          <Text style={[styles.title, isDarkMode && styles.titleDark]}>
            {currentPost?.title}
          </Text>
          <View style={[styles.metaContainer, isDarkMode && styles.metaDark]}>
            <Text style={[styles.meta, isDarkMode && styles.metaDark]}>
              {new Date(currentPost?.published_date).toLocaleDateString()}
            </Text>
            <Text style={[styles.meta, isDarkMode && styles.metaDark]}>
              By {[currentPost?.author_first_name, currentPost?.author_last_name].filter(Boolean).join(' ') || 'Anonymous'}
            </Text>
          </View>
          <View style={styles.contentPadding}>
            <RenderHtml
              contentWidth={width}
              source={{ html: currentPost?.content || '' }}
              tagsStyles={tagsStyles}
              renderersProps={renderersProps}
            />
          </View>

          <View style={styles.contentPadding}>
            <RenderHtml
              contentWidth={width}
              source={{ html: currentPost?.content || "" }}
              tagsStyles={tagsStyles}
              renderersProps={renderersProps}
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
  },
  containerDark: {
    backgroundColor: "#121212",
  },
  content: {
    flex: 1,
  },
  contentDark: {
    backgroundColor: "#121212",
  },
  header: {
    marginBottom: 20,
  },
  imageContainer: {
    position: "relative",
    width: "100%",
    height: 300,
    marginBottom: 20,
  },
  image: {
    width: "100%",
    height: "100%",
    backgroundColor: "#f0f0f0",
  },
  imageOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  gradient: {
    flex: 1,
    justifyContent: "flex-end",
    padding: 15,
  },
  actions: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: 15,
    marginBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#000000",
    marginBottom: 10,
    paddingHorizontal: 15,
  },
  titleDark: {
    color: "#ffffff",
  },
  metaContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
    paddingHorizontal: 15,
  },
  meta: {
    fontSize: 14,
    color: "#666666",
  },
  metaDark: {
    color: "#a0a0a0",
  },
  errorText: {
    color: "#ff3b30",
    textAlign: "center",
    marginTop: 20,
  },
  errorTextDark: {
    color: "#ff6b6b",
  },
  contentPadding: {
    paddingHorizontal: 15,
  },
});
