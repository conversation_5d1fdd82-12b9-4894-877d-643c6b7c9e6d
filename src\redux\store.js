import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';
import postsReducer from './postsSlice';
import authReducer from './authSlice';
import blogSaveReducer from './blogSaveSlice';

const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  whitelist: ['posts', 'auth', 'blogSave'], // Persist posts, auth, and blogSave
};

const rootReducer = combineReducers({
  posts: postsReducer,
  auth: authReducer,
  blogSave: blogSaveReducer,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export const persistor = persistStore(store);
