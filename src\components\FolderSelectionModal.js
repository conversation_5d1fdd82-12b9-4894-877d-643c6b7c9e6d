import React, { useState, useEffect, useContext } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  FlatList,
  TextInput,
  StyleSheet,
  Alert,
} from 'react-native';
import * as Icons from '@expo/vector-icons';
import { useDispatch, useSelector } from 'react-redux';
import { ThemeContext } from '../context/ThemeContext';
import { fetchFolders, createFolder } from '../redux/blogSaveSlice';

export const FolderSelectionModal = ({ 
  visible, 
  onClose, 
  onSelectFolder, 
  selectedFolderId = null 
}) => {
  const dispatch = useDispatch();
  const { isDarkMode } = useContext(ThemeContext);
  const { folders, isLoading } = useSelector((state) => state.blogSave);
  const { isAuthenticated } = useSelector((state) => state.auth);
  
  const [showCreateFolder, setShowCreateFolder] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [newFolderDescription, setNewFolderDescription] = useState('');

  useEffect(() => {
    if (visible && isAuthenticated) {
      dispatch(fetchFolders());
    }
  }, [visible, isAuthenticated, dispatch]);

  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) {
      Alert.alert('Error', 'Please enter a folder name');
      return;
    }

    try {
      const result = await dispatch(createFolder({
        name: newFolderName.trim(),
        description: newFolderDescription.trim() || null,
      })).unwrap();
      
      setNewFolderName('');
      setNewFolderDescription('');
      setShowCreateFolder(false);
      
      // Auto-select the newly created folder
      onSelectFolder(result.id);
    } catch (error) {
      Alert.alert('Error', error.message || 'Failed to create folder');
    }
  };

  const renderFolderItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.folderItem,
        isDarkMode && styles.folderItemDark,
        selectedFolderId === item.id && styles.selectedFolder,
        selectedFolderId === item.id && isDarkMode && styles.selectedFolderDark,
      ]}
      onPress={() => onSelectFolder(item.id)}
    >
      <View style={styles.folderInfo}>
        <Icons.Ionicons
          name="folder"
          size={20}
          color={selectedFolderId === item.id ? '#007AFF' : (isDarkMode ? '#fff' : '#333')}
        />
        <View style={styles.folderText}>
          <Text style={[
            styles.folderName,
            isDarkMode && styles.folderNameDark,
            selectedFolderId === item.id && styles.selectedText
          ]}>
            {item.name}
          </Text>
          {item.description && (
            <Text style={[
              styles.folderDescription,
              isDarkMode && styles.folderDescriptionDark,
              selectedFolderId === item.id && styles.selectedText
            ]}>
              {item.description}
            </Text>
          )}
          <Text style={[
            styles.folderCount,
            isDarkMode && styles.folderCountDark,
            selectedFolderId === item.id && styles.selectedText
          ]}>
            {item.saved_blogs_count} articles
          </Text>
        </View>
      </View>
      {selectedFolderId === item.id && (
        <Icons.Ionicons name="checkmark" size={20} color="#007AFF" />
      )}
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.container, isDarkMode && styles.containerDark]}>
        <View style={[styles.header, isDarkMode && styles.headerDark]}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Icons.Ionicons 
              name="close" 
              size={24} 
              color={isDarkMode ? '#fff' : '#333'} 
            />
          </TouchableOpacity>
          <Text style={[styles.title, isDarkMode && styles.titleDark]}>
            Select Folder
          </Text>
          <TouchableOpacity 
            onPress={() => setShowCreateFolder(true)}
            style={styles.addButton}
          >
            <Icons.Ionicons 
              name="add" 
              size={24} 
              color="#007AFF" 
            />
          </TouchableOpacity>
        </View>

        {showCreateFolder && (
          <View style={[styles.createFolderSection, isDarkMode && styles.createFolderSectionDark]}>
            <TextInput
              style={[styles.input, isDarkMode && styles.inputDark]}
              placeholder="Folder name"
              placeholderTextColor={isDarkMode ? '#888' : '#666'}
              value={newFolderName}
              onChangeText={setNewFolderName}
              maxLength={100}
            />
            <TextInput
              style={[styles.input, styles.descriptionInput, isDarkMode && styles.inputDark]}
              placeholder="Description (optional)"
              placeholderTextColor={isDarkMode ? '#888' : '#666'}
              value={newFolderDescription}
              onChangeText={setNewFolderDescription}
              multiline
              maxLength={500}
            />
            <View style={styles.createFolderButtons}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={() => {
                  setShowCreateFolder(false);
                  setNewFolderName('');
                  setNewFolderDescription('');
                }}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, styles.createButton]}
                onPress={handleCreateFolder}
              >
                <Text style={styles.createButtonText}>Create</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        <View style={styles.folderSection}>
          <TouchableOpacity
            style={[
              styles.folderItem,
              isDarkMode && styles.folderItemDark,
              selectedFolderId === null && styles.selectedFolder,
              selectedFolderId === null && isDarkMode && styles.selectedFolderDark,
            ]}
            onPress={() => onSelectFolder(null)}
          >
            <View style={styles.folderInfo}>
              <Icons.Ionicons
                name="bookmark-outline"
                size={20}
                color={selectedFolderId === null ? '#007AFF' : (isDarkMode ? '#fff' : '#333')}
              />
              <Text style={[
                styles.folderName,
                isDarkMode && styles.folderNameDark,
                selectedFolderId === null && styles.selectedText
              ]}>
                No Folder (General)
              </Text>
            </View>
            {selectedFolderId === null && (
              <Icons.Ionicons name="checkmark" size={20} color="#007AFF" />
            )}
          </TouchableOpacity>

          <FlatList
            data={folders}
            renderItem={renderFolderItem}
            keyExtractor={(item) => item.id.toString()}
            style={styles.folderList}
            showsVerticalScrollIndicator={false}
          />
        </View>

        <View style={styles.footer}>
          <TouchableOpacity
            style={[styles.button, styles.saveButton]}
            onPress={onClose}
          >
            <Text style={styles.saveButtonText}>Done</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  containerDark: {
    backgroundColor: '#1a1a1a',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerDark: {
    borderBottomColor: '#333',
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  titleDark: {
    color: '#fff',
  },
  addButton: {
    padding: 8,
  },
  createFolderSection: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  createFolderSectionDark: {
    borderBottomColor: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    fontSize: 16,
    color: '#333',
  },
  inputDark: {
    borderColor: '#444',
    backgroundColor: '#2a2a2a',
    color: '#fff',
  },
  descriptionInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  createFolderButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
  },
  button: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
  },
  cancelButtonText: {
    color: '#666',
    fontWeight: '500',
  },
  createButton: {
    backgroundColor: '#007AFF',
  },
  createButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
  folderSection: {
    flex: 1,
    padding: 16,
  },
  folderList: {
    flex: 1,
  },
  folderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    backgroundColor: '#f8f8f8',
  },
  folderItemDark: {
    backgroundColor: '#2a2a2a',
  },
  selectedFolder: {
    backgroundColor: '#e3f2fd',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  selectedFolderDark: {
    backgroundColor: '#1e3a5f',
  },
  folderInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  folderText: {
    marginLeft: 12,
    flex: 1,
  },
  folderName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  folderNameDark: {
    color: '#fff',
  },
  folderDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  folderDescriptionDark: {
    color: '#aaa',
  },
  folderCount: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  folderCountDark: {
    color: '#777',
  },
  selectedText: {
    color: '#007AFF',
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  saveButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
