import React, { useContext, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { getStudentProfile } from '../../redux/authSlice';
import { ThemeContext } from '../../context/ThemeContext';
import { MaterialIcons } from '@expo/vector-icons';

export default function ProfileScreen() {
  const dispatch = useDispatch();
  const { isDarkMode } = useContext(ThemeContext);

  const { student, profile, loading, error } = useSelector((state) => state.auth);

  useEffect(() => {
    if (student?.id) {
      dispatch(getStudentProfile({ id: student.id }));
    }
  }, [dispatch, student]);

  if (loading) {
    return (
      <SafeAreaView style={[styles.center, isDarkMode && styles.containerDark]}>
        <ActivityIndicator size="large" color={isDarkMode ? "#fff" : "#000"} />
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={[styles.center, isDarkMode && styles.containerDark]}>
        <Text style={[styles.errorText, isDarkMode && styles.valueDark]}>
          {error.toString()}
        </Text>
      </SafeAreaView>
    );
  }

  if (!profile || !profile.student) {
    return (
      <SafeAreaView style={[styles.center, isDarkMode && styles.containerDark]}>
        <Text style={[styles.value, isDarkMode && styles.valueDark]}>
          No profile data available.
        </Text>
      </SafeAreaView>
    );
  }

  const { user, phone, course, subscription_type } = profile.student;

  const fields = [
    { label: 'Name', value: `${user.first_name} ${user.last_name}`, icon: 'person' },
    { label: 'Email', value: user.email, icon: 'email' },
    { label: 'Phone', value: phone || '—', icon: 'phone' },
    { label: 'Course', value: course || '—', icon: 'school' },
    { label: 'Subscription Type', value: subscription_type || '—', icon: 'card-membership' },
  ];

  return (
    <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
      <View style={[styles.content, isDarkMode && styles.contentDark]}>
        {/* <Text style={[styles.title, isDarkMode && styles.titleDark]}>
          Profile
        </Text> */}
        {fields.map(({ label, value, icon }) => (
          <View key={label} style={[styles.section, isDarkMode && styles.sectionDark]}>
            <View style={styles.labelRow}>
              <MaterialIcons
                name={icon}
                size={20}
                color={isDarkMode ? '#ccc' : '#666'}
                style={styles.icon}
              />
              <Text style={[styles.label, isDarkMode && styles.labelDark]}>
                {label}
              </Text>
            </View>
            <Text style={[styles.value, isDarkMode && styles.valueDark]}>{value}</Text>
          </View>
        ))}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
  },
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  containerDark: {
    backgroundColor: '#121212',
  },
  content: {
    flex: 1,
    padding: 20,
    backgroundColor: '#ffffff',
  },
  contentDark: {
    backgroundColor: '#121212',
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 24,
    color: '#222',
    textAlign: 'center',
  },
  titleDark: {
    color: '#eee',
  },
  section: {
    backgroundColor: '#f9f9f9',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 10,
    shadowOffset: { width: 0, height: 4 },
    elevation: 3,
  },
  sectionDark: {
    backgroundColor: '#1e1e1e',
    shadowColor: '#000',
    shadowOpacity: 0.2,
  },
  labelRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  icon: {
    marginRight: 8,
  },
  label: {
    fontSize: 15,
    color: '#666666',
    fontWeight: '600',
  },
  labelDark: {
    color: '#aaa',
  },
  value: {
    fontSize: 18,
    color: '#111111',
    fontWeight: '500',
  },
  valueDark: {
    color: '#eee',
  },
  errorText: {
    fontSize: 16,
    color: '#ff3b30',
  },
});
