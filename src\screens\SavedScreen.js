import React, { useContext, useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  RefreshControl,
  Alert
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import * as Icons from '@expo/vector-icons';
import { Card } from '../components/Card';
import { ThemeContext } from '../context/ThemeContext';
import { FolderSelectionModal } from '../components/FolderSelectionModal';
import {
  fetchFolders,
  fetchSavedBlogs,
  createFolder,
  deleteFolder
} from '../redux/blogSaveSlice';

export default function SavedScreen({ navigation }) {
  const dispatch = useDispatch();
  const { isDarkMode } = useContext(ThemeContext);
  const { isAuthenticated } = useSelector((state) => state.auth);
  const {
    folders,
    savedBlogs,
    isLoading,
    error,
    needsAuth
  } = useSelector((state) => state.blogSave);

  const [selectedFolderId, setSelectedFolderId] = useState(null);
  const [showFolderModal, setShowFolderModal] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (isAuthenticated) {
      dispatch(fetchFolders());
      dispatch(fetchSavedBlogs());
    }
  }, [isAuthenticated, dispatch]);

  useEffect(() => {
    if (needsAuth) {
      Alert.alert(
        'Login Required',
        'Please log in to view your saved articles.',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Login',
            onPress: () => navigation.navigate('Auth')
          }
        ]
      );
    }
  }, [needsAuth, navigation]);

  const onRefresh = async () => {
    if (!isAuthenticated) return;

    setRefreshing(true);
    try {
      await Promise.all([
        dispatch(fetchFolders()),
        dispatch(fetchSavedBlogs(selectedFolderId))
      ]);
    } finally {
      setRefreshing(false);
    }
  };

  const handleFolderSelect = (folderId) => {
    setSelectedFolderId(folderId);
    dispatch(fetchSavedBlogs(folderId));
  };

  const handleDeleteFolder = (folder) => {
    Alert.alert(
      'Delete Folder',
      `Are you sure you want to delete "${folder.name}"? Articles in this folder will be moved to "No Folder".`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            dispatch(deleteFolder(folder.id));
            if (selectedFolderId === folder.id) {
              setSelectedFolderId(null);
              dispatch(fetchSavedBlogs());
            }
          }
        }
      ]
    );
  };

  const getFullImageUrl = (imageUrl) => {
    if (!imageUrl) return null;
    return imageUrl.startsWith('http')
      ? imageUrl
      : `${process.env.EXPO_PUBLIC_BASE_URL}${imageUrl}`.replace(/([^:]\/)\/+/g, "$1");
  };

  const renderFolderTab = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.folderTab,
        isDarkMode && styles.folderTabDark,
        selectedFolderId === item.id && styles.selectedFolderTab,
        selectedFolderId === item.id && isDarkMode && styles.selectedFolderTabDark,
      ]}
      onPress={() => handleFolderSelect(item.id)}
      onLongPress={() => handleDeleteFolder(item)}
    >
      <Icons.Ionicons
        name="folder"
        size={16}
        color={selectedFolderId === item.id ? '#007AFF' : (isDarkMode ? '#fff' : '#333')}
      />
      <Text style={[
        styles.folderTabText,
        isDarkMode && styles.folderTabTextDark,
        selectedFolderId === item.id && styles.selectedFolderTabText
      ]}>
        {item.name}
      </Text>
      <Text style={[
        styles.folderTabCount,
        isDarkMode && styles.folderTabCountDark,
        selectedFolderId === item.id && styles.selectedFolderTabCount
      ]}>
        {item.saved_blogs_count}
      </Text>
    </TouchableOpacity>
  );

  if (!isAuthenticated) {
    return (
      <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
        <View style={[styles.emptyContainer, isDarkMode && styles.emptyContainerDark]}>
          <Icons.Ionicons
            name="bookmark-outline"
            size={64}
            color={isDarkMode ? '#666' : '#ccc'}
          />
          <Text style={[styles.emptyText, isDarkMode && styles.emptyTextDark]}>
            Please log in to view saved articles
          </Text>
          <TouchableOpacity
            style={styles.loginButton}
            onPress={() => navigation.navigate('Auth')}
          >
            <Text style={styles.loginButtonText}>Login</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const filteredBlogs = selectedFolderId
    ? savedBlogs.filter(sb => sb.folder?.id === selectedFolderId)
    : savedBlogs.filter(sb => !sb.folder);

  return (
    <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
      {/* Folder Tabs */}
      <View style={[styles.folderSection, isDarkMode && styles.folderSectionDark]}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.folderScrollContent}
        >
          <TouchableOpacity
            style={[
              styles.folderTab,
              isDarkMode && styles.folderTabDark,
              selectedFolderId === null && styles.selectedFolderTab,
              selectedFolderId === null && isDarkMode && styles.selectedFolderTabDark,
            ]}
            onPress={() => handleFolderSelect(null)}
          >
            <Icons.Ionicons
              name="bookmark-outline"
              size={16}
              color={selectedFolderId === null ? '#007AFF' : (isDarkMode ? '#fff' : '#333')}
            />
            <Text style={[
              styles.folderTabText,
              isDarkMode && styles.folderTabTextDark,
              selectedFolderId === null && styles.selectedFolderTabText
            ]}>
              All
            </Text>
          </TouchableOpacity>

          <FlatList
            data={folders}
            renderItem={renderFolderTab}
            keyExtractor={(item) => item.id.toString()}
            horizontal
            showsHorizontalScrollIndicator={false}
          />

          <TouchableOpacity
            style={[styles.addFolderTab, isDarkMode && styles.addFolderTabDark]}
            onPress={() => setShowFolderModal(true)}
          >
            <Icons.Ionicons
              name="add"
              size={16}
              color="#007AFF"
            />
          </TouchableOpacity>
        </ScrollView>
      </View>

      {/* Saved Articles */}
      {filteredBlogs.length === 0 ? (
        <View style={[styles.emptyContainer, isDarkMode && styles.emptyContainerDark]}>
          <Icons.Ionicons
            name="bookmark-outline"
            size={64}
            color={isDarkMode ? '#666' : '#ccc'}
          />
          <Text style={[styles.emptyText, isDarkMode && styles.emptyTextDark]}>
            {selectedFolderId ? 'No articles in this folder' : 'No saved articles yet'}
          </Text>
          <Text style={[styles.emptySubtext, isDarkMode && styles.emptySubtextDark]}>
            Tap the bookmark icon on any article to save it
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredBlogs}
          renderItem={({ item: savedBlog }) => (
            <Card
              title={savedBlog.blog.title}
              shortContent={savedBlog.blog.short_content}
              imageUrl={getFullImageUrl(savedBlog.blog.image)}
              date={new Date(savedBlog.blog.published_date).toLocaleDateString()}
              authorFirstName={savedBlog.blog.author_first_name}
              authorLastName={savedBlog.blog.author_last_name}
              viewsCount={savedBlog.blog.views_counts}
              onPress={() => navigation.navigate('BlogPost', { slug: savedBlog.blog.slug })}
              post={savedBlog.blog}
              navigation={navigation}
            />
          )}
          keyExtractor={item => item.id.toString()}
          contentContainerStyle={[styles.listContent, isDarkMode && styles.listContentDark]}
          style={[styles.flatList, isDarkMode && styles.flatListDark]}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor={isDarkMode ? '#fff' : '#000'}
            />
          }
        />
      )}

      <FolderSelectionModal
        visible={showFolderModal}
        onClose={() => setShowFolderModal(false)}
        onSelectFolder={() => setShowFolderModal(false)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  containerDark: {
    backgroundColor: '#1a1a1a',
  },
  folderSection: {
    backgroundColor: '#f8f8f8',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingVertical: 8,
  },
  folderSectionDark: {
    backgroundColor: '#2a2a2a',
    borderBottomColor: '#333',
  },
  folderScrollContent: {
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  folderTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 16,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  folderTabDark: {
    backgroundColor: '#3a3a3a',
    borderColor: '#555',
  },
  selectedFolderTab: {
    backgroundColor: '#e3f2fd',
    borderColor: '#007AFF',
  },
  selectedFolderTabDark: {
    backgroundColor: '#1e3a5f',
    borderColor: '#007AFF',
  },
  folderTabText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  folderTabTextDark: {
    color: '#fff',
  },
  selectedFolderTabText: {
    color: '#007AFF',
  },
  folderTabCount: {
    marginLeft: 6,
    fontSize: 12,
    color: '#666',
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    minWidth: 20,
    textAlign: 'center',
  },
  folderTabCountDark: {
    color: '#ccc',
    backgroundColor: '#555',
  },
  selectedFolderTabCount: {
    color: '#007AFF',
    backgroundColor: '#fff',
  },
  addFolderTab: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
    borderWidth: 1,
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  addFolderTabDark: {
    backgroundColor: '#3a3a3a',
    borderColor: '#555',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyContainerDark: {
    backgroundColor: '#1a1a1a',
  },
  emptyText: {
    fontSize: 18,
    color: '#666666',
    textAlign: 'center',
    marginTop: 16,
    fontWeight: '500',
  },
  emptyTextDark: {
    color: '#cccccc',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginTop: 8,
  },
  emptySubtextDark: {
    color: '#777',
  },
  loginButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  listContent: {
    paddingBottom: 20,
  },
  listContentDark: {
    backgroundColor: '#1a1a1a',
  },
  flatList: {
    flex: 1,
  },
  flatListDark: {
    backgroundColor: '#1a1a1a',
  },
});
