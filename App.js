import 'react-native-gesture-handler';
import { StatusBar } from 'expo-status-bar';
import React, { useContext, useEffect } from 'react';
import { Provider, useDispatch, useSelector } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from './src/redux/store';
import AppNavigator from './src/navigation/AppNavigator';
import Toast from 'react-native-toast-message';
import { NavigationContainer } from '@react-navigation/native';
import { ThemeProvider, ThemeContext } from './src/context/ThemeContext';
import { SafeAreaView, Alert } from 'react-native';
import { migrateLegacySavedPosts, hasLegacySavedPosts } from './src/utils/migrationHelper';

function AppContent() {
  const { isDarkMode } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const { isAuthenticated } = useSelector((state) => state.auth);
  const { savedPosts } = useSelector((state) => state.posts);

  useEffect(() => {
    // Check for legacy saved posts and offer migration
    if (isAuthenticated && hasLegacySavedPosts(savedPosts)) {
      Alert.alert(
        'Migrate Saved Articles',
        `You have ${savedPosts.length} locally saved articles. Would you like to migrate them to your cloud collection for better sync across devices?`,
        [
          {
            text: 'Skip',
            style: 'cancel',
          },
          {
            text: 'Migrate',
            onPress: async () => {
              await migrateLegacySavedPosts(savedPosts, dispatch, isAuthenticated);
            },
          },
        ]
      );
    }
  }, [isAuthenticated, savedPosts, dispatch]);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <NavigationContainer>
        <AppNavigator />
      </NavigationContainer>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} translucent={false} />
      <Toast />
    </SafeAreaView>
  );
}

export default function App() {
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <ThemeProvider>
          <AppContent />
        </ThemeProvider>
      </PersistGate>
    </Provider>
  );
}
