{"name": "blog_native_javascript", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/masked-view": "^0.1.11", "@react-native-google-signin/google-signin": "^15.0.0", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.10.0", "expo": "~53.0.20", "expo-auth-session": "^6.2.1", "expo-constants": "^17.1.7", "expo-file-system": "^18.1.11", "expo-font": "^13.3.2", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-status-bar": "~2.2.3", "expo-web-browser": "^14.2.0", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-toast-message": "^2.3.3", "react-native-vector-icons": "^10.2.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["redux-persist", "@react-native-community/masked-view", "react-native-vector-icons"], "listUnknownPackages": false}}}, "private": true}