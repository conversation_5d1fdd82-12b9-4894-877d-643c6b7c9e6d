import React, { useState, useEffect, useContext } from 'react';
import { View, Text, StyleSheet, FlatList, SafeAreaView, RefreshControl, Dimensions } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { Card } from '../components/Card';
import { Button } from '../components/Button';
import { SkeletonLoader } from '../components/SkeletonLoader';
import { truncateText } from '../utils/helpers';
import { fetchPosts } from '../redux/postsSlice';
import { ThemeContext } from '../context/ThemeContext';

export default function HomeScreen({ navigation }) {
  const dispatch = useDispatch();
  const { items: posts, isLoading, error } = useSelector((state) => state.posts);
  const [refreshing, setRefreshing] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);  const windowHeight = Dimensions.get('window').height;
  const { isDarkMode } = useContext(ThemeContext);

  const loadPosts = async () => {
    try {
      await dispatch(fetchPosts()).unwrap();
    } catch (err) {
      console.error('Failed to load posts:', err);
    }
  };

  useEffect(() => {
    loadPosts();
  }, []);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    await loadPosts();
    setCurrentIndex(0);
    setRefreshing(false);
  }, []);

  const handleScroll = (event) => {
    const offsetY = event.nativeEvent.contentOffset.y;
    const newIndex = Math.round(offsetY / windowHeight);
    if (newIndex !== currentIndex && newIndex >= 0 && newIndex < posts.length) {
      setCurrentIndex(newIndex);
    }
  };

  const getFullImageUrl = (imageUrl) => {
    if (!imageUrl) return null;
    return imageUrl.startsWith('http') 
      ? imageUrl 
      : `${process.env.EXPO_PUBLIC_BASE_URL}${imageUrl}`.replace(/([^:]\/)\/+/g, "$1");
  };

  if (isLoading && !refreshing) {
    return (
      <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
        <SkeletonLoader />
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
        <View style={[styles.errorContainer, isDarkMode && styles.errorContainerDark]}>
          <Text style={[styles.errorText, isDarkMode && styles.textDark]}>
            {typeof error === 'string' ? error : 'Failed to load posts'}
          </Text>
          <Button title="Retry" onPress={loadPosts} />
        </View>
      </SafeAreaView>
    );
  }

  if (!posts || posts.length === 0) {
    return (
      <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
        <View style={[styles.emptyContainer, isDarkMode && styles.emptyContainerDark]}>
          <Text style={[styles.emptyText, isDarkMode && styles.textDark]}>No posts available</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>      
      <FlatList
        data={posts}
        renderItem={({ item: post, index }) => (
          <Card
            title={post.title}
            shortContent={post.short_content}
            imageUrl={getFullImageUrl(post.image)}
            date={new Date(post.published_date).toLocaleDateString()}
            authorFirstName={post.author_first_name}
            authorLastName={post.author_last_name}
            viewsCount={post.views_counts}            onPress={() => navigation.navigate('BlogPost', { 
              slug: post.slug,
              title: truncateText(post.title, 20),
              fromPreview: true
            })}            post={post}
            isDarkMode={isDarkMode}
            nextPost={index < posts.length - 1 ? posts[index + 1] : null}
            fromPreview={false}
            navigation={navigation}
          />
        )}
        keyExtractor={post => post.id.toString()}
        pagingEnabled
        snapToInterval={windowHeight}
        snapToAlignment="start"
        decelerationRate="fast"
        onMomentumScrollEnd={handleScroll}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={isDarkMode ? '#ffffff' : '#000000'}
            colors={[isDarkMode ? '#ffffff' : '#000000']}
          />
        }
        style={[styles.flatList, isDarkMode && styles.flatListDark]}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  containerDark: {
    backgroundColor: '#121212',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#ffffff',
  },
  errorContainerDark: {
    backgroundColor: '#121212',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#ffffff',
  },
  emptyContainerDark: {
    backgroundColor: '#121212',
  },
  errorText: {
    fontSize: 16,
    color: '#ff3b30',
    marginBottom: 20,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
  textDark: {
    color: '#e0e0e0',
  },
  flatList: {
    backgroundColor: '#ffffff',
  },
  flatListDark: {
    backgroundColor: '#121212',
  },
});
