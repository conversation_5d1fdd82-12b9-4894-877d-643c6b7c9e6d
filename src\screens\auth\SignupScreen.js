import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import * as Icons from '@expo/vector-icons';
import { useDispatch, useSelector } from 'react-redux';
import { registerStudent, verifyOTP } from '../../redux/authSlice';
import Toast from 'react-native-toast-message';
import * as Google from 'expo-auth-session/providers/google';
import * as WebBrowser from 'expo-web-browser';

WebBrowser.maybeCompleteAuthSession();

const STEPS = {
  PERSONAL_INFO: 1,
  CONTACT_INFO: 2,
  REFERRAL: 3,
  OTP: 4,
};

export default function SignupScreen({ navigation }) {
  const dispatch = useDispatch();
  const { isLoading, error } = useSelector((state) => state.auth);

  const [step, setStep] = useState(STEPS.PERSONAL_INFO);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    username: '',
    phone: '',
    email: '',
    password: '',
    confirmPassword: '',
    course: '',
    referralCode: '',
  });
  const [otp, setOtp] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const [request, response, promptAsync] = Google.useAuthRequest({
    expoClientId: process.env.EXPO_PUBLIC_GOOGLE_CLIENT_ID,
    androidClientId: process.env.EXPO_PUBLIC_ANDROID_CLIENT_ID,
    iosClientId: process.env.EXPO_PUBLIC_IOS_CLIENT_ID,
  });

  const validateStep = (currentStep) => {
    switch (currentStep) {
      case STEPS.PERSONAL_INFO:
        if (!formData.firstName.trim() || !formData.lastName.trim()) {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: 'Please fill in all fields',
          });
          return false;
        }
        break;
      case STEPS.CONTACT_INFO:
        if (!formData.email.trim() || !formData.password.trim() || !formData.confirmPassword.trim()) {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: 'Please fill in all fields',
          });
          return false;
        }
        if (formData.password !== formData.confirmPassword) {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: 'Passwords do not match',
          });
          return false;
        }
        if (formData.password.length < 6) {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: 'Password must be at least 6 characters',
          });
          return false;
        }
        break;
      case STEPS.OTP:
        if (!otp.trim() || otp.length !== 6) {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: 'Please enter a valid OTP',
          });
          return false;
        }
        break;
    }
    return true;
  };

  const handleNext = async () => {
    if (!validateStep(step)) return;

    if (step === STEPS.CONTACT_INFO) {
      try {
        const userData = {
          user: {
            first_name: formData.firstName,
            last_name: formData.lastName,
            email: formData.email,
            username: formData.username,
            password: formData.password,
          },
          phone: formData.phone,
          course: formData.course,
          referral_code: formData.referralCode || null,
        };        await dispatch(registerStudent({ studentData: userData })).unwrap();
        Toast.show({
          type: 'success',
          text1: 'Registration Successful!',
          text2: 'Please check your email for OTP',
          visibilityTime: 3000,
          autoHide: true,
        });
        setStep(STEPS.REFERRAL);
      } catch (err) {
        Toast.show({
          type: 'error',
          text1: 'Registration Failed',
          text2: err?.message || 'Please check your information and try again',
          visibilityTime: 3000,
          autoHide: true,
        });
        return;
      }
    } else {
      setStep(step + 1);
    }
  };

  const handleBack = () => {
    setStep(step - 1);
  };

  const handleVerifyOTP = async () => {
    try {
      await dispatch(
        verifyOTP({
          otpData: {
            otp: otp,
            email_user: formData.email,
          },
        })
      ).unwrap();
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'OTP verified successfully',
      });
      navigation.navigate('Login');
    } catch (err) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: err || 'OTP verification failed',
      });
    }
  };

  const renderStep = () => {
    switch (step) {
      case STEPS.PERSONAL_INFO:
        return (
          <>
            <TextInput
              style={styles.input}
              placeholder="First Name"
              value={formData.firstName}
              onChangeText={(text) => setFormData({ ...formData, firstName: text })}
            />
            <TextInput
              style={styles.input}
              placeholder="Last Name"
              value={formData.lastName}
              onChangeText={(text) => setFormData({ ...formData, lastName: text })}
            />
          </>
        );
      case STEPS.CONTACT_INFO:
        return (
          <>
            <TextInput
              style={styles.input}
              placeholder="Username"
              value={formData.username}
              onChangeText={(text) => setFormData({ ...formData, username: text })}
              autoCapitalize="none"
            />
            <TextInput
              style={styles.input}
              placeholder="Phone (10 digits)"
              value={formData.phone}
              onChangeText={(text) => {
                if (text.length <= 10) {
                  setFormData({ ...formData, phone: text });
                }
              }}
              keyboardType="phone-pad"
            />
            <TextInput
              style={styles.input}
              placeholder="Email"
              value={formData.email}
              onChangeText={(text) => setFormData({ ...formData, email: text })}
              keyboardType="email-address"
              autoCapitalize="none"
            />            <View style={styles.passwordContainer}>
              <TextInput
                style={[styles.input, styles.passwordInput]}
                placeholder="Password"
                value={formData.password}
                onChangeText={(text) => setFormData({ ...formData, password: text })}
                secureTextEntry={!showPassword}
              />
              <TouchableOpacity
                style={styles.eyeIcon}
                onPress={() => setShowPassword(!showPassword)}
              >                <Icons.Ionicons
                  name={showPassword ? 'eye-off-outline' : 'eye-outline'}
                  size={24}
                  color="#666"
                />
              </TouchableOpacity>
            </View>
            <View style={styles.passwordContainer}>
              <TextInput
                style={[styles.input, styles.passwordInput]}
                placeholder="Confirm Password"
                value={formData.confirmPassword}
                onChangeText={(text) => setFormData({ ...formData, confirmPassword: text })}
                secureTextEntry={!showConfirmPassword}
              />
              <TouchableOpacity
                style={styles.eyeIcon}
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                <Ionicons
                  name={showConfirmPassword ? 'eye-off-outline' : 'eye-outline'}
                  size={24}
                  color="#666"
                />
              </TouchableOpacity>
            </View>
          </>
        );
      case STEPS.REFERRAL:
        return (
          <TextInput
            style={styles.input}
            placeholder="Referral Code (Optional)"
            value={formData.referralCode}
            onChangeText={(text) => setFormData({ ...formData, referralCode: text })}
          />
        );
      case STEPS.OTP:
        return (
          <TextInput
            style={styles.input}
            placeholder="Enter OTP"
            value={otp}
            onChangeText={setOtp}
            keyboardType="number-pad"
            maxLength={6}
          />
        );
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Text style={styles.title}>Create Account</Text>
        <Text style={styles.subtitle}>
          {step === STEPS.PERSONAL_INFO
            ? 'Personal Information'
            : step === STEPS.CONTACT_INFO
            ? 'Contact Information'
            : step === STEPS.REFERRAL
            ? 'Referral Code'
            : 'Verify OTP'}
        </Text>

        <View style={styles.form}>
          {renderStep()}

          <View style={styles.buttonContainer}>
            {step > STEPS.PERSONAL_INFO && (
              <TouchableOpacity
                style={[styles.button, styles.backButton]}
                onPress={handleBack}
              >
                <Text style={[styles.buttonText, styles.backButtonText]}>Back</Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={[styles.button, styles.nextButton]}
              onPress={step === STEPS.OTP ? handleVerifyOTP : handleNext}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="#ffffff" />
              ) : (
                <Text style={styles.buttonText}>
                  {step === STEPS.OTP ? 'Verify OTP' : 'Continue'}
                </Text>
              )}
            </TouchableOpacity>
          </View>         {step === STEPS.PERSONAL_INFO && (
  <View style={styles.footer}>
    <Text style={styles.footerText}>Already have an account?</Text>
    <TouchableOpacity onPress={() => navigation.navigate('Login')}>
      <Text style={styles.loginText}>Login</Text>
    </TouchableOpacity>
  </View>
)}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 30,
    textAlign: 'center',
  },
  form: {
    width: '100%',
  },
  input: {
    backgroundColor: '#f0f0f0',
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
    fontSize: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  button: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  nextButton: {
    backgroundColor: '#28a745',
  },
  backButton: {
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#28a745',
  },
  googleButton: {
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  backButtonText: {
    color: '#28a745',
  },
  googleButtonText: {
    color: '#333',
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 20,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#ddd',
  },
  dividerText: {
    color: '#666',
    marginHorizontal: 10,
  },
  // ✅ NEW STYLES FOR FOOTER SECTION
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  footerText: {
    color: '#666',
    marginRight: 5,
  },
  loginText: {
    color: '#28a745',
    fontWeight: '600',
  },
});
