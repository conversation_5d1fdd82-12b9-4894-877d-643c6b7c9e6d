import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { folderApi, savedBlogApi } from '../api/blogSaveApi';
import Toast from 'react-native-toast-message';

// Async thunks for folder management
export const fetchFolders = createAsyncThunk(
  'blogSave/fetchFolders',
  async (_, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      if (!auth.JWT_Token) {
        return rejectWithValue({ needsAuth: true, message: 'Please log in to view folders' });
      }
      
      const response = await folderApi.getFolders(auth.JWT_Token);
      return response.data.data;
    } catch (error) {
      return rejectWithValue({
        needsAuth: error.needsAuth || false,
        message: error.response?.data?.message || error.message || 'Failed to fetch folders'
      });
    }
  }
);

export const createFolder = createAsyncThunk(
  'blogSave/createFolder',
  async (folderData, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      if (!auth.JWT_Token) {
        return rejectWithValue({ needsAuth: true, message: 'Please log in to create folders' });
      }
      
      const response = await folderApi.createFolder(folderData, auth.JWT_Token);
      Toast.show({
        type: 'success',
        text1: 'Folder Created',
        text2: `"${folderData.name}" folder created successfully`,
      });
      return response.data.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to create folder';
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: message,
      });
      return rejectWithValue({
        needsAuth: error.needsAuth || false,
        message
      });
    }
  }
);

export const updateFolder = createAsyncThunk(
  'blogSave/updateFolder',
  async ({ folderId, folderData }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      if (!auth.JWT_Token) {
        return rejectWithValue({ needsAuth: true, message: 'Please log in to update folders' });
      }
      
      const response = await folderApi.updateFolder(folderId, folderData, auth.JWT_Token);
      Toast.show({
        type: 'success',
        text1: 'Folder Updated',
        text2: 'Folder updated successfully',
      });
      return response.data.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to update folder';
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: message,
      });
      return rejectWithValue({
        needsAuth: error.needsAuth || false,
        message
      });
    }
  }
);

export const deleteFolder = createAsyncThunk(
  'blogSave/deleteFolder',
  async (folderId, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      if (!auth.JWT_Token) {
        return rejectWithValue({ needsAuth: true, message: 'Please log in to delete folders' });
      }
      
      await folderApi.deleteFolder(folderId, auth.JWT_Token);
      Toast.show({
        type: 'success',
        text1: 'Folder Deleted',
        text2: 'Folder deleted successfully',
      });
      return folderId;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to delete folder';
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: message,
      });
      return rejectWithValue({
        needsAuth: error.needsAuth || false,
        message
      });
    }
  }
);

// Async thunks for blog saving
export const saveBlog = createAsyncThunk(
  'blogSave/saveBlog',
  async ({ slug, folderId = null, notes = '' }, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      if (!auth.JWT_Token) {
        return rejectWithValue({ needsAuth: true, message: 'Please log in to save articles' });
      }
      
      const saveData = { folder_id: folderId, notes };
      const response = await savedBlogApi.saveBlog(slug, saveData, auth.JWT_Token);
      
      Toast.show({
        type: 'success',
        text1: 'Article Saved',
        text2: 'Article saved to your collection',
      });
      
      return response.data.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to save article';
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: message,
      });
      return rejectWithValue({
        needsAuth: error.needsAuth || false,
        message
      });
    }
  }
);

export const fetchSavedBlogs = createAsyncThunk(
  'blogSave/fetchSavedBlogs',
  async (folderId = null, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      if (!auth.JWT_Token) {
        return rejectWithValue({ needsAuth: true, message: 'Please log in to view saved articles' });
      }
      
      const response = await savedBlogApi.getSavedBlogs(folderId, auth.JWT_Token);
      return response.data.data;
    } catch (error) {
      return rejectWithValue({
        needsAuth: error.needsAuth || false,
        message: error.response?.data?.message || error.message || 'Failed to fetch saved articles'
      });
    }
  }
);

export const unsaveBlog = createAsyncThunk(
  'blogSave/unsaveBlog',
  async (savedBlogId, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      if (!auth.JWT_Token) {
        return rejectWithValue({ needsAuth: true, message: 'Please log in to manage saved articles' });
      }
      
      await savedBlogApi.unsaveBlog(savedBlogId, auth.JWT_Token);
      
      Toast.show({
        type: 'success',
        text1: 'Article Removed',
        text2: 'Article removed from your collection',
      });
      
      return savedBlogId;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to remove article';
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: message,
      });
      return rejectWithValue({
        needsAuth: error.needsAuth || false,
        message
      });
    }
  }
);

export const checkSavedStatus = createAsyncThunk(
  'blogSave/checkSavedStatus',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState();
      if (!auth.JWT_Token) {
        return { slug, isSaved: false, savedBlogId: null };
      }
      
      const response = await savedBlogApi.checkSaved(slug, auth.JWT_Token);
      return {
        slug,
        isSaved: response.data.data.is_saved,
        savedBlogId: response.data.data.saved_blog_id
      };
    } catch (error) {
      return { slug, isSaved: false, savedBlogId: null };
    }
  }
);

const initialState = {
  folders: [],
  savedBlogs: [],
  savedStatus: {}, // { slug: { isSaved: boolean, savedBlogId: number } }
  isLoading: false,
  error: null,
  needsAuth: false,
};

const blogSaveSlice = createSlice({
  name: 'blogSave',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
      state.needsAuth = false;
    },
    clearSavedBlogs: (state) => {
      state.savedBlogs = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // Folders
      .addCase(fetchFolders.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchFolders.fulfilled, (state, action) => {
        state.isLoading = false;
        state.folders = action.payload;
      })
      .addCase(fetchFolders.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload.message;
        state.needsAuth = action.payload.needsAuth;
      })
      
      .addCase(createFolder.fulfilled, (state, action) => {
        state.folders.push(action.payload);
      })
      
      .addCase(updateFolder.fulfilled, (state, action) => {
        const index = state.folders.findIndex(f => f.id === action.payload.id);
        if (index !== -1) {
          state.folders[index] = action.payload;
        }
      })
      
      .addCase(deleteFolder.fulfilled, (state, action) => {
        state.folders = state.folders.filter(f => f.id !== action.payload);
      })
      
      // Saved blogs
      .addCase(fetchSavedBlogs.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSavedBlogs.fulfilled, (state, action) => {
        state.isLoading = false;
        state.savedBlogs = action.payload;
      })
      .addCase(fetchSavedBlogs.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload.message;
        state.needsAuth = action.payload.needsAuth;
      })
      
      .addCase(saveBlog.fulfilled, (state, action) => {
        const savedBlog = action.payload;
        state.savedBlogs.push(savedBlog);
        state.savedStatus[savedBlog.blog.slug] = {
          isSaved: true,
          savedBlogId: savedBlog.id
        };
      })
      
      .addCase(unsaveBlog.fulfilled, (state, action) => {
        const savedBlogId = action.payload;
        const savedBlog = state.savedBlogs.find(sb => sb.id === savedBlogId);
        if (savedBlog) {
          state.savedStatus[savedBlog.blog.slug] = {
            isSaved: false,
            savedBlogId: null
          };
        }
        state.savedBlogs = state.savedBlogs.filter(sb => sb.id !== savedBlogId);
      })
      
      .addCase(checkSavedStatus.fulfilled, (state, action) => {
        const { slug, isSaved, savedBlogId } = action.payload;
        state.savedStatus[slug] = { isSaved, savedBlogId };
      });
  },
});

export const { clearError, clearSavedBlogs } = blogSaveSlice.actions;
export default blogSaveSlice.reducer;
