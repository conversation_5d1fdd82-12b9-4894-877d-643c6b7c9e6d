import { saveBlog } from '../redux/blogSaveSlice';
import { clearLegacySavedPosts } from '../redux/postsSlice';
import Toast from 'react-native-toast-message';

/**
 * Migrates locally saved posts to server-based saving
 * @param {Array} legacySavedPosts - Posts saved in local storage
 * @param {Function} dispatch - Redux dispatch function
 * @param {boolean} isAuthenticated - Whether user is authenticated
 * @returns {Promise<{success: number, failed: number}>}
 */
export const migrateLegacySavedPosts = async (legacySavedPosts, dispatch, isAuthenticated) => {
  if (!isAuthenticated) {
    Toast.show({
      type: 'info',
      text1: 'Login Required',
      text2: 'Please log in to migrate your saved articles to the cloud',
    });
    return { success: 0, failed: 0 };
  }

  if (!Array.isArray(legacySavedPosts) || legacySavedPosts.length === 0) {
    return { success: 0, failed: 0 };
  }

  let successCount = 0;
  let failedCount = 0;

  Toast.show({
    type: 'info',
    text1: 'Migrating Saved Articles',
    text2: `Migrating ${legacySavedPosts.length} articles to your cloud collection...`,
  });

  // Process posts in batches to avoid overwhelming the server
  const batchSize = 5;
  for (let i = 0; i < legacySavedPosts.length; i += batchSize) {
    const batch = legacySavedPosts.slice(i, i + batchSize);
    
    const batchPromises = batch.map(async (post) => {
      try {
        if (post.slug) {
          await dispatch(saveBlog({
            slug: post.slug,
            folderId: null, // Save to general folder
            notes: 'Migrated from local storage'
          })).unwrap();
          successCount++;
        } else {
          failedCount++;
        }
      } catch (error) {
        console.warn(`Failed to migrate post ${post.id}:`, error);
        failedCount++;
      }
    });

    await Promise.allSettled(batchPromises);
    
    // Small delay between batches
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  // Clear legacy saved posts after successful migration
  if (successCount > 0) {
    dispatch(clearLegacySavedPosts());
  }

  // Show migration results
  if (successCount > 0) {
    Toast.show({
      type: 'success',
      text1: 'Migration Complete',
      text2: `${successCount} articles migrated successfully${failedCount > 0 ? `, ${failedCount} failed` : ''}`,
    });
  } else if (failedCount > 0) {
    Toast.show({
      type: 'error',
      text1: 'Migration Failed',
      text2: `Failed to migrate ${failedCount} articles. Please try again.`,
    });
  }

  return { success: successCount, failed: failedCount };
};

/**
 * Checks if user has legacy saved posts that need migration
 * @param {Array} legacySavedPosts - Posts saved in local storage
 * @returns {boolean}
 */
export const hasLegacySavedPosts = (legacySavedPosts) => {
  return Array.isArray(legacySavedPosts) && legacySavedPosts.length > 0;
};

/**
 * Shows migration prompt to user
 * @param {number} count - Number of legacy posts
 * @param {Function} onMigrate - Callback when user chooses to migrate
 * @param {Function} onSkip - Callback when user chooses to skip
 */
export const showMigrationPrompt = (count, onMigrate, onSkip) => {
  Toast.show({
    type: 'info',
    text1: 'Migrate Saved Articles',
    text2: `You have ${count} locally saved articles. Migrate them to your cloud collection?`,
    visibilityTime: 0, // Don't auto-hide
    autoHide: false,
    onPress: onMigrate,
    props: {
      onPress: onMigrate,
      onHide: onSkip,
    }
  });
};
