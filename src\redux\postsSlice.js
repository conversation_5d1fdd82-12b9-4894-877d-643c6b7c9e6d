import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import Constants from 'expo-constants';

// Fetch all blogs
export const fetchPosts = createAsyncThunk(
  'posts/fetchPosts',
  async (_, { rejectWithValue }) => {
    try {
      const baseURL = Constants.expoConfig.extra.EXPO_PUBLIC_BASE_URL;
      const blogsPath = Constants.expoConfig.extra.EXPO_PUBLIC_BLOGS;
      const url = `${baseURL}${blogsPath}`;
      console.log('Fetching blogs from URL:', url);

      const response = await axios.get(url);

      if (!Array.isArray(response.data)) {
        throw new Error('Invalid response format');
      }

      return response.data;
    } catch (error) {
      console.error('Error details:', {
        message: error.message,
        response: error.response?.data,
      });
      return rejectWithValue(
        typeof error.response?.data === 'string' 
          ? error.response.data 
          : error.message || 'Failed to fetch posts'
      );
    }
  }
);

// Fetch a specific blog by slug
export const getPost = createAsyncThunk(
  'posts/getPost',
  async (slug, { rejectWithValue }) => {
    try {
      const baseURL = Constants.expoConfig.extra.EXPO_PUBLIC_BASE_URL;
      const blogsPath = Constants.expoConfig.extra.EXPO_PUBLIC_BLOGS;
      const url = `${baseURL}${blogsPath}${slug}/`;

      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      return rejectWithValue(
        typeof error.response?.data === 'string'
          ? error.response.data
          : error.message || 'Failed to fetch post'
      );
    }
  }
);

const postsSlice = createSlice({
  name: 'posts',
  initialState: {
    items: [],
    currentPost: null,
    savedPosts: [], // Kept for backward compatibility, but use blogSave slice for new saves
    isLoading: false,
    error: null,
  },
  reducers: {
    // Deprecated: Use blogSave slice for new implementations
    toggleSavePost: (state, action) => {
      if (!Array.isArray(state.savedPosts)) {
        state.savedPosts = [];
      }

      const post = action.payload;
      const isPostSaved = state.savedPosts.some(savedPost => savedPost?.id === post?.id);

      if (isPostSaved) {
        state.savedPosts = state.savedPosts.filter(savedPost => savedPost.id !== post.id);
      } else {
        state.savedPosts.push(post);
      }
    },
    clearLegacySavedPosts: (state) => {
      state.savedPosts = [];
    }
  },
  extraReducers: (builder) => {
    builder
      // Handle fetchPosts actions
      .addCase(fetchPosts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPosts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.items = action.payload;
      })
      .addCase(fetchPosts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Handle getPost actions
      .addCase(getPost.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getPost.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentPost = action.payload;
      })
      .addCase(getPost.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export const { toggleSavePost, clearLegacySavedPosts } = postsSlice.actions;
export default postsSlice.reducer;
