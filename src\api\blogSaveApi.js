import axios from 'axios';
import Constants from 'expo-constants';

const baseURL = Constants.expoConfig.extra.EXPO_PUBLIC_BASE_URL;

// Create axios instance with base configuration
const api = axios.create({
  baseURL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include JWT token
api.interceptors.request.use(
  (config) => {
    // Token will be added by Redux middleware
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      return Promise.reject({
        ...error,
        needsAuth: true,
        message: 'Please log in to save articles'
      });
    }
    return Promise.reject(error);
  }
);

// Folder Management APIs
export const folderApi = {
  // Get all folders
  getFolders: (token) => 
    api.get('/api/blogs/saved-folders/', {
      headers: { Authorization: `Bearer ${token}` }
    }),

  // Create new folder
  createFolder: (folderData, token) =>
    api.post('/api/blogs/saved-folders/', folderData, {
      headers: { Authorization: `Bearer ${token}` }
    }),

  // Get folder with saved blogs
  getFolder: (folderId, token) =>
    api.get(`/api/blogs/saved-folders/${folderId}/`, {
      headers: { Authorization: `Bearer ${token}` }
    }),

  // Update folder
  updateFolder: (folderId, folderData, token) =>
    api.put(`/api/blogs/saved-folders/${folderId}/`, folderData, {
      headers: { Authorization: `Bearer ${token}` }
    }),

  // Delete folder
  deleteFolder: (folderId, token) =>
    api.delete(`/api/blogs/saved-folders/${folderId}/`, {
      headers: { Authorization: `Bearer ${token}` }
    }),
};

// Blog Saving APIs
export const savedBlogApi = {
  // Save a blog
  saveBlog: (slug, saveData, token) =>
    api.post(`/api/blogs/save/${slug}/`, saveData, {
      headers: { Authorization: `Bearer ${token}` }
    }),

  // Get all saved blogs
  getSavedBlogs: (folderId = null, token) => {
    const url = folderId 
      ? `/api/blogs/saved-blogs/?folder_id=${folderId}`
      : '/api/blogs/saved-blogs/';
    return api.get(url, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  // Get saved blog details
  getSavedBlog: (savedBlogId, token) =>
    api.get(`/api/blogs/saved-blogs/${savedBlogId}/`, {
      headers: { Authorization: `Bearer ${token}` }
    }),

  // Update saved blog (move folder, edit notes)
  updateSavedBlog: (savedBlogId, updateData, token) =>
    api.put(`/api/blogs/saved-blogs/${savedBlogId}/`, updateData, {
      headers: { Authorization: `Bearer ${token}` }
    }),

  // Remove from saved collection
  unsaveBlog: (savedBlogId, token) =>
    api.delete(`/api/blogs/saved-blogs/${savedBlogId}/`, {
      headers: { Authorization: `Bearer ${token}` }
    }),

  // Check if blog is saved
  checkSaved: (slug, token) =>
    api.get(`/api/blogs/check-saved/${slug}/`, {
      headers: { Authorization: `Bearer ${token}` }
    }),
};

export default { folderApi, savedBlogApi };
